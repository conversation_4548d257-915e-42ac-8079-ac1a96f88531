import { httpRequest } from './http';

//活动操作前缀
const operationLogin = '/ausnutria/repurchase/userc';
const operation = '/ausnutria/repurchase';

export const lzLogin = (params) => {
  return httpRequest({
    operation: '登陆接口',
    url: `${operationLogin}/login`,
    method: 'POST',
    params,
  });
};

export const syncUser = (params) => {
  return httpRequest({
    operation: '同步用户信息',
    url: `${operationLogin}/syncUser`,
    method: 'POST',
    params,
  });
};

export const addNewMember = (params) => {
  return httpRequest({
    operation: '新增会员上报',
    url: `${operation}/addNewMember`,
    method: 'POST',
    params,
  });
};

export const getActivityInfo = (params) => {
  return httpRequest({
    operation: '活动详情',
    url: `${operation}/getActivityInfo`,
    method: 'POST',
    params,
  });
};

export const getActivitId = (params) => {
  return httpRequest({
    operation: '获取活动ID',
    url: `${operation}/getActivityId`,
    method: 'POST',
    params,
  });
};

export const applyPrize = (params) => {
  return httpRequest({
    operation: '报名参与',
    url: `${operation}/sign`,
    method: 'POST',
    params,
  });
};

export const receivePrize = (params) => {
  return httpRequest({
    operation: '领取奖品',
    url: `${operation}/receivePrize`,
    method: 'POST',
    params,
  });
};

export const getSkuPages = (params) => {
  return httpRequest({
    operation: '获取曝光商品',
    url: `${operation}/getSkuPagesByType`,
    method: 'POST',
    params,
  });
};

export const getUserReceiveRecord = (params) => {
  return httpRequest({
    operation: '用户领取记录',
    url: `${operation}/getUserReceiveRecord`,
    method: 'POST',
    params,
  });
};

export const addAccessLog = (params) => {
  return httpRequest({
    operation: '埋点',
    url: `${operation}/addAccessLog`,
    method: 'POST',
    params,
  });
};

export const saveAddress = (params) => {
  return httpRequest({
    operation: '保存地址',
    url: `${operation}/saveAddress`,
    method: 'POST',
    params,
  });
};

export const getAddressDetail = (params) => {
  return httpRequest({
    operation: '获取地址详情',
    url: `${operation}/getAddressDetail`,
    method: 'POST',
    params,
  });
};
