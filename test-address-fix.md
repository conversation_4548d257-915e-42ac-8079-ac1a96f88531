# 地址回显修复测试

## 问题描述
兑礼记录页面点击查看地址时，地址信息未能正确回显。

## 修复内容

### 1. Store修改 (store/store.js)
- 添加了 `currentAddressInfo` 字段用于存储当前选中的地址信息
- 添加了 `setCurrentAddressInfo` 方法用于设置地址信息

### 2. API修改 (utils/constants/api.js)
- 添加了 `getAddressDetail` API用于获取地址详情（如果后端支持）

### 3. 父页面修改 (pages/index/index.js)
- 在 `handleOpenAddress` 方法中添加了地址信息保存逻辑
- 尝试调用 `getAddressDetail` API获取详细地址信息
- 如果API调用失败，则使用记录中传递的地址信息
- 将地址信息保存到 store 中

### 4. Address-pop组件修改 (components/address-pop/address-pop.js)
- 添加了 `attached` 生命周期方法
- 添加了 `loadAddressInfo` 方法用于加载和回显地址信息
- 支持多种地址字段格式的解析：
  - region数组格式
  - region字符串格式（JSON解析）
  - 分离的province/city/county字段
- 在关闭弹窗时清除地址信息
- 添加了 reportClick 导入

## 测试步骤

1. 启动项目
2. 进入兑礼记录页面
3. 点击任意一条已完成的记录的"查看地址"按钮
4. 验证地址弹窗中是否正确显示了保存的地址信息：
   - 姓名
   - 手机号
   - 省市区
   - 详细地址

## 预期结果
- 地址信息应该正确回显在对应的输入框中
- 如果是已完成的地址，输入框应该是禁用状态
- 关闭弹窗后，地址信息应该被清除，不影响下次使用

## 技术细节
- 使用 store 作为中介传递地址信息
- 支持多种地址字段格式（region数组、字符串、或分离的province/city/county字段）
- 在组件挂载时自动加载地址信息
- 在组件关闭时自动清理地址信息
- 优雅降级：如果获取地址详情API失败，使用记录中的地址信息

## 可能的问题和解决方案

### 1. 如果后端不支持 getAddressDetail API
- 修复会优雅降级到使用记录中的地址信息
- 可以移除 getAddressDetail API调用，直接使用记录信息

### 2. 如果地址字段名不匹配
- loadAddressInfo 方法支持多种字段名格式
- 可以根据实际API返回的字段名调整映射逻辑

### 3. 如果需要支持更多地址字段
- 在 loadAddressInfo 方法中添加对应的字段处理逻辑
- 在模板文件中添加对应的输入框
