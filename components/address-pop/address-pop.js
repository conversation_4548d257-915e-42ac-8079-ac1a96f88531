import create from 'mini-stores';
import { saveAddress } from '../../utils/constants/api';
import { showToast } from '../../utils/utils';
const store = require('../../store/store');
const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
const app = getApp();

create.Component(stores, {
  data: {
    showRegionPicker: false, // 控制地区选择器的显示与隐藏
    realName: "", // 姓名
    mobile: "", // 手机号
    region: ["", "", ""], // 地区
    addressDetail: "", // 详细地址
  },
  properties: {},
  created() {},
  attached() {
    // 组件挂载时检查是否有地址信息需要回显
    this.loadAddressInfo();
  },
  methods: {
    // 加载地址信息
    loadAddressInfo() {
      const addressInfo = store.data.currentAddressInfo;
      if (addressInfo) {
        console.log('加载地址信息:', addressInfo);
        // 根据API返回的字段名设置地址信息
        const updateData = {};

        // 设置姓名
        if (addressInfo.realName) {
          updateData.realName = addressInfo.realName;
        }

        // 设置手机号
        if (addressInfo.mobile) {
          updateData.mobile = addressInfo.mobile;
        }

        // 设置省市区
        if (addressInfo.region) {
          // 如果region是数组格式
          if (Array.isArray(addressInfo.region)) {
            updateData.region = addressInfo.region;
          }
          // 如果region是字符串格式，需要解析
          else if (typeof addressInfo.region === 'string') {
            try {
              updateData.region = JSON.parse(addressInfo.region);
            } catch (e) {
              console.warn('解析region失败:', e);
              updateData.region = ["", "", ""];
            }
          }
        } else {
          // 如果没有region字段，尝试从province, city, county字段组合
          const province = addressInfo.province || '';
          const city = addressInfo.city || '';
          const county = addressInfo.county || addressInfo.district || '';
          updateData.region = [province, city, county];
        }

        // 设置详细地址
        if (addressInfo.addressDetail) {
          updateData.addressDetail = addressInfo.addressDetail;
        }

        console.log('设置地址数据:', updateData);
        this.setData(updateData);
      }
    },
    onReceiverInput(e) {
      this.setData({ realName: e.detail.value });
    },
    
    onMobileInput(e) {
      this.setData({ mobile: e.detail.value });
    },
    
    onDetailInput(e) {
      this.setData({ addressDetail: e.detail.value });
    },
      // 显示地区选择器
    showRegionPicker() {
      this.setData({ showRegionPicker: true });
    },
    // 省市区选择变化事件
    bindRegionChange: function(e) {
      this.setData({
        region: e.detail.value
      });
    },
    // 检查表单
    checkForm () {
      const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
      console.log(this.data);
      if (!this.data.realName || this.data.realName === '') {
        tt.showToast({
          title: '请输入姓名',
          icon: 'none',
        });
      } else if (!this.data.mobile || this.data.mobile === '') {
        tt.showToast({
          title: '请输入手机号',
          icon: 'none',
        });
      } else if (!phone.test(this.data.mobile)) {
        tt.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
      } else if (this.data.region[0] === '' || this.data.region[1] === '' || this.data.region[2] === '') {
        tt.showToast({
          title: '请选择省市区',
          icon: 'none',
        });
      } else if (!this.data.addressDetail || this.data.addressDetail === '') {
        tt.showToast({
          title: '请输入详细地址',
          icon: 'none',
        });
      } else {
        this.submit();
      }
    },
    async submit () {
      // 提交表单
      store.setAddressForm(
        {
          realName: this.data.realName, // 姓名
          mobile: this.data.mobile, // 手机号
          region: this.data.region, // 地址-省市区
          addressDetail: this.data.addressDetail, // 详细地址
        }
      );
      try {
        await saveAddress({
          realName: this.data.realName, // 姓名
          mobile: this.data.mobile, // 手机号
          region: this.data.region, // 地址-省市区
          addressDetail: this.data.addressDetail, // 详细地址
        });
        console.log('地址保存成功');
        reportClick('保存地址');
        showToast('地址保存成功');
      } catch (error) {
        console.error('地址保存失败:', error);
        showToast(error.message || '地址保存失败，请重试');
      }
      console.log('提交表单', store.data.addressForm);
      // 关闭当前弹窗
      this.triggerEvent('close');
    },
    onClose() {
      this.triggerEvent('close');
    },
  },

});
